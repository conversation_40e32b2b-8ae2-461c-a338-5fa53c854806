import 'package:shared_preferences/shared_preferences.dart';

class SettingsService {
  static Future<void> saveDefaultHz(String mode, int hz) async {
    final sp = await SharedPreferences.getInstance();
    await sp.setInt('default_hz_$mode', hz);
  }

  static Future<int?> getDefaultHz(String mode) async {
    final sp = await SharedPreferences.getInstance();
    return sp.getInt('default_hz_$mode');
  }
}

