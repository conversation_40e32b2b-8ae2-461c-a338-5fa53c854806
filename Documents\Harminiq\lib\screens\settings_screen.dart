import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});
  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  double _mainVol = 0.8;
  double _ambVol = 0.4;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final sp = await SharedPreferences.getInstance();
    setState(() {
      _mainVol = sp.getDouble('main_vol') ?? 0.8;
      _ambVol = sp.getDouble('amb_vol') ?? 0.4;
    });
  }

  Future<void> _save() async {
    final sp = await SharedPreferences.getInstance();
    await sp.setDouble('main_vol', _mainVol);
    await sp.setDouble('amb_vol', _ambVol);
    if (mounted) {
      ScaffoldMessenger.of(context)
          .showSnackBar(const SnackBar(content: Text('Saved')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Settings')),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          const Text('Main Volume', style: TextStyle(fontWeight: FontWeight.bold)),
          Slider(value: _mainVol, onChanged: (v) => setState(() => _mainVol = v)),
          const SizedBox(height: 16),
          const Text('Ambient Volume', style: TextStyle(fontWeight: FontWeight.bold)),
          Slider(value: _ambVol, onChanged: (v) => setState(() => _ambVol = v)),
          const SizedBox(height: 16),
          ElevatedButton(onPressed: _save, child: const Text('Save')),
        ],
      ),
    );
  }
}

