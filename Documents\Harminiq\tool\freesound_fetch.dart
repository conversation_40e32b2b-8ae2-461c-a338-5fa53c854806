// tool/freesound_fetch.dart
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:dio/dio.dart';

/// ==== CONFIG (supplied by you) ====
const String clientId = 'ptXPUfeB5HEUWNn8blkn';
const String clientSecret = '7iFSf8enpjkddTyWxVDXvCjxmJuGlXJ2VCTWjFKt';
const String redirectUri = 'http://freesound.org/home/<USER>/permission_granted/';

/// Frequencies to fetch:
const List<int> freqs = [111,174,256,285,396,417,432,528,639,741,852,963,1024];

Future<void> main(List<String> args) async {
  print('=== Harmoniq Freesound Fetch Tool ===');

  // 1) Ask user to authorize and paste ?code=...
  final authUrl = Uri.parse(
    'https://freesound.org/apiv2/oauth2/authorize/?client_id=$clientId'
    '&response_type=code&redirect_uri=$redirectUri'
  );
  print('\nOpen this URL in your browser and authorize the app:');
  print(authUrl.toString());
  stdout.write('\nPaste the "code" query parameter from the redirect URL here: ');
  final code = stdin.readLineSync()?.trim();
  if (code == null || code.isEmpty) {
    stderr.writeln('No code provided. Exiting.');
    exit(1);
  }

  // 2) Exchange code -> access token
  final token = await _exchangeCodeForToken(code);
  print('Access token obtained.');

  // 3) Ensure assets/audio exists
  final audioDir = Directory('assets/audio');
  if (!audioDir.existsSync()) {
    audioDir.createSync(recursive: true);
    print('Created ${audioDir.path}');
  }

  // 4) For each frequency: search & download best preview (mp3 preferred, ogg fallback)
  final dio = Dio();
  for (final hz in freqs) {
    final best = await _searchBestMatch(hz, token);
    if (best == null) {
      stderr.writeln('No result for $hz Hz — skipping.');
      continue;
    }
    final previews = best['previews'] as Map<String, dynamic>?;
    final url = previews?['preview-hq-mp3'] ?? previews?['preview-lq-mp3'] ?? previews?['preview-hq-ogg'] ?? previews?['preview-lq-ogg'];
    if (url == null) {
      stderr.writeln('No preview URL for $hz Hz — skipping.');
      continue;
    }
    final isMp3 = url.toString().toLowerCase().endsWith('.mp3');
    final ext = isMp3 ? 'mp3' : 'ogg';
    final filename = '${hz}hz.$ext';
    final savePath = 'assets/audio/$filename';
    print('Downloading $hz Hz -> $savePath');
    await dio.download(url, savePath);
  }

  print('\nDone. Add these to pubspec.yaml under flutter: assets: or just use the folder include:');
  print('  assets:\n    - assets/audio/\n');
  print('Then: flutter pub get');
}

/// Exchange authorization code for an access_token
Future<String> _exchangeCodeForToken(String code) async {
  final resp = await http.post(
    Uri.parse('https://freesound.org/apiv2/oauth2/access_token/'),
    body: {
      'client_id': clientId,
      'client_secret': clientSecret,
      'grant_type': 'authorization_code',
      'code': code,
      'redirect_uri': redirectUri,
    },
  );
  if (resp.statusCode != 200) {
    stderr.writeln('Token exchange failed: ${resp.statusCode} ${resp.body}');
    exit(1);
  }
  final data = jsonDecode(resp.body) as Map<String, dynamic>;
  return data['access_token'] as String;
}

/// Search Freesound and return the "best" candidate (simple heuristic)
Future<Map<String, dynamic>?> _searchBestMatch(int hz, String token) async {
  // If specifically looking for 639 Hz, broaden search and paginate more
  if (hz == 639) {
    final broaderQueries = [
      '639 hz sine', '639hz sine', '639 sine tone', '639 hz tone', '639 tone', '639',
    ];
    for (final q in broaderQueries) {
      for (int page = 1; page <= 3; page++) {
        final u = Uri.parse('https://freesound.org/apiv2/search/text/'
            '?query=${Uri.encodeQueryComponent(q)}'
            '&filter=duration:[0.5 TO 240]'
            '&fields=id,name,previews,download,username,license'
            '&page_size=30&page=$page');
        final resp = await http.get(u, headers: {'Authorization': 'Bearer $token'});
        if (resp.statusCode != 200) continue;
        final json = jsonDecode(resp.body) as Map<String, dynamic>;
        final results = (json['results'] as List).cast<Map<String, dynamic>>();
        if (results.isEmpty) continue;
        results.sort((a, b) {
          final an = (a['name'] ?? '').toString().toLowerCase();
          final bn = (b['name'] ?? '').toString().toLowerCase();
          // Scores: exact patterns best, then contains "639"
          int score(String n) {
            if (n.contains('639 hz') || n.contains('639hz')) return 0;
            if (n.contains('639')) return 1;
            return 2;
          }
          return score(an).compareTo(score(bn));
        });
        return results.first;
      }
    }
    // fall through to default approach if nothing found
  }
  // Prefer "hz" + "sine" or "tone"
  final queries = [
    '$hz hz sine',
    '$hz hz tone',
    '$hz sine tone',
    '$hz tone',
  ];
  for (final q in queries) {
    final u = Uri.parse('https://freesound.org/apiv2/search/text/'
        '?query=${Uri.encodeQueryComponent(q)}'
        '&filter=duration:[0.5 TO 180]'
        '&fields=id,name,previews,download,username,license'
        '&page_size=10');
    final resp = await http.get(u, headers: {'Authorization': 'Bearer $token'});
    if (resp.statusCode != 200) continue;
    final json = jsonDecode(resp.body) as Map<String, dynamic>;
    final results = (json['results'] as List).cast<Map<String, dynamic>>();
    if (results.isEmpty) continue;

    // Simple pick: prefer items whose name contains the exact hz substring
    results.sort((a, b) {
      final an = (a['name'] ?? '').toString().toLowerCase();
      final bn = (b['name'] ?? '').toString().toLowerCase();
      final ascore = an.contains('$hz') ? 0 : 1;
      final bscore = bn.contains('$hz') ? 0 : 1;
      return ascore.compareTo(bscore);
    });
    return results.first;
  }
  return null;
}

